<?php

namespace App\Http\Controllers;

use App\Models\PetHealthRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PetHealthRecordController extends Controller
{
    public function index()
    {
        $records = PetHealthRecord::where('user_id', Auth::id())->get();
        return view('pet_health.index', compact('records'));
    }

    public function create()
    {
        return view('pet_health.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'pet_name' => 'required|string|max:255',
            'species' => 'required|string|max:255',
            'breed' => 'nullable|string|max:255',
            'medical_history' => 'nullable|string|max:1000',
            'last_vaccination' => 'nullable|date',
        ], [
            'pet_name.required' => 'Pet name is required.',
            'species.required' => 'Species is required.',
            'last_vaccination.date' => 'Please enter a valid date for vaccination.',
        ]);

        PetHealthRecord::create([
            'user_id' => Auth::id(),
            'pet_name' => $request->pet_name,
            'species' => $request->species,
            'breed' => $request->breed,
            'medical_history' => $request->medical_history,
            'last_vaccination' => $request->last_vaccination,
        ]);

        return redirect()->route('pet_health.index')->with('success', 'Pet health record saved successfully!');
    }

    public function edit(PetHealthRecord $petHealthRecord)
    {
        // Ensure the user can only edit their own records
        if ($petHealthRecord->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        return view('pet_health.edit', compact('petHealthRecord'));
    }

    public function update(Request $request, PetHealthRecord $petHealthRecord)
    {
        // Ensure the user can only update their own records
        if ($petHealthRecord->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'pet_name' => 'required|string|max:255',
            'species' => 'required|string|max:255',
            'breed' => 'nullable|string|max:255',
            'medical_history' => 'nullable|string|max:1000',
            'last_vaccination' => 'nullable|date',
        ], [
            'pet_name.required' => 'Pet name is required.',
            'species.required' => 'Species is required.',
            'last_vaccination.date' => 'Please enter a valid date for vaccination.',
        ]);

        $petHealthRecord->update([
            'pet_name' => $request->pet_name,
            'species' => $request->species,
            'breed' => $request->breed,
            'medical_history' => $request->medical_history,
            'last_vaccination' => $request->last_vaccination,
        ]);

        return redirect()->route('pet_health.index')->with('success', 'Pet health record updated successfully!');
    }

    public function destroy(PetHealthRecord $petHealthRecord)
    {
        // Ensure the user can only delete their own records
        if ($petHealthRecord->user_id !== Auth::id()) {
            abort(403, 'Unauthorized action.');
        }

        $petHealthRecord->delete();

        return redirect()->route('pet_health.index')->with('success', 'Pet health record deleted successfully!');
    }
}
