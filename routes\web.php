<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\PetHealthRecordController;

Route::get('/', function () {
    return redirect('/login');
});

// USER ROUTES
Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
Route::post('/register', [AuthController::class, 'register']);

// DASHBOARD ROUTE WITH AUTH MIDDLEWARE
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', function () {
        $user = Auth::user();

        // Get pet health analytics
        $totalPets = \App\Models\PetHealthRecord::where('user_id', $user->id)->count();
        $petsWithVaccinations = \App\Models\PetHealthRecord::where('user_id', $user->id)
            ->whereNotNull('last_vaccination')
            ->count();
        $petsWithMedicalHistory = \App\Models\PetHealthRecord::where('user_id', $user->id)
            ->whereNotNull('medical_history')
            ->where('medical_history', '!=', '')
            ->count();
        $recentVaccinations = \App\Models\PetHealthRecord::where('user_id', $user->id)
            ->where('last_vaccination', '>=', now()->subMonths(6))
            ->count();

        // Get species breakdown
        $speciesBreakdown = \App\Models\PetHealthRecord::where('user_id', $user->id)
            ->selectRaw('species, COUNT(*) as count')
            ->groupBy('species')
            ->pluck('count', 'species')
            ->toArray();

        return view('dashboard', compact(
            'totalPets',
            'petsWithVaccinations',
            'petsWithMedicalHistory',
            'recentVaccinations',
            'speciesBreakdown'
        ));
    })->name('dashboard');


    // PET HEALTH RECORD ROUTES
    Route::get('/pet-health', [PetHealthRecordController::class, 'index'])->name('pet_health.index');
    Route::get('/pet-health/create', [PetHealthRecordController::class, 'create'])->name('pet_health.create');
    Route::post('/pet-health', [PetHealthRecordController::class, 'store'])->name('pet_health.store');
    Route::get('/pet-health/{petHealthRecord}/edit', [PetHealthRecordController::class, 'edit'])->name('pet_health.edit');
    Route::put('/pet-health/{petHealthRecord}', [PetHealthRecordController::class, 'update'])->name('pet_health.update');
    Route::delete('/pet-health/{petHealthRecord}', [PetHealthRecordController::class, 'destroy'])->name('pet_health.destroy');

    // ADDITIONAL ROUTES
    Route::get('/pet-adoption', fn() => view('adoption.index'))->name('pet_adoption');
    Route::get('/online-consultation', fn() => view('consultation.index'))->name('online_consultation');
    Route::get('/lost-found', fn() => view('lostfound.index'))->name('lost_found');
    Route::get('/multi-pet', fn() => view('multipet.index'))->name('multi_pet');
});

// ADMIN ROUTES
Route::get('/admin/login', [AdminController::class, 'showLogin'])->name('admin.login');
Route::post('/admin/login', [AdminController::class, 'login']);
Route::get('/admin/dashboard', [AdminController::class, 'dashboard'])
    ->middleware(['auth', 'admin'])
    ->name('admin.dashboard');

// LOGOUT
Route::post('/logout', function () {
    Auth::logout();
    return redirect()->route('login')->with('success', 'You have been logged out.');
})->name('logout');