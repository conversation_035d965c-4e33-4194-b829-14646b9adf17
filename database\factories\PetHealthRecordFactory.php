<?php

namespace Database\Factories;

use App\Models\PetHealthRecord;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class PetHealthRecordFactory extends Factory
{
    protected $model = PetHealthRecord::class;

    public function definition()
    {
        return [
            'user_id' => User::factory(),
            'pet_name' => $this->faker->firstName(),
            'species' => $this->faker->randomElement(['Dog', '<PERSON>', 'Bird', 'Rabbit', 'Hamster']),
            'breed' => $this->faker->randomElement([
                'Golden Retriever', 'Labrador', 'German Shepherd', 'Persian Cat', 
                'Siamese Cat', 'Maine Coon', 'Parakeet', 'Canary', 'Holland Lop', 
                'Syrian Hamster'
            ]),
            'medical_history' => $this->faker->optional()->sentence(),
            'last_vaccination' => $this->faker->optional()->date(),
        ];
    }
}
