<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePetHealthRecordsTable extends Migration
{
    public function up()
    {
        Schema::create('pet_health_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('pet_name');
            $table->string('species');
            $table->string('breed')->nullable();
            $table->text('medical_history')->nullable();
            $table->date('last_vaccination')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('pet_health_records');
    }
}
