<?php $__env->startSection('title', 'Online Consultations'); ?>

<?php $__env->startSection('content'); ?>
<div class="consultation-header">
    <div class="header-content">
        <h1>🩺 Online Consultations</h1>
        <p>Book and manage your pet's veterinary consultations from the comfort of your home.</p>
        <a href="<?php echo e(route('consultation.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Book New Consultation
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon total">📊</div>
        <div class="stat-content">
            <h3><?php echo e($stats['total']); ?></h3>
            <p>Total Consultations</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon pending">⏳</div>
        <div class="stat-content">
            <h3><?php echo e($stats['pending']); ?></h3>
            <p>Pending</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon confirmed">✅</div>
        <div class="stat-content">
            <h3><?php echo e($stats['confirmed']); ?></h3>
            <p>Confirmed</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon completed">🎉</div>
        <div class="stat-content">
            <h3><?php echo e($stats['completed']); ?></h3>
            <p>Completed</p>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="filters-section">
    <form method="GET" action="<?php echo e(route('consultation.index')); ?>" class="filters-form">
        <div class="filter-group">
            <label for="status">Status:</label>
            <select name="status" id="status" onchange="this.form.submit()">
                <option value="all" <?php echo e(request('status') === 'all' || !request('status') ? 'selected' : ''); ?>>All Status</option>
                <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                <option value="confirmed" <?php echo e(request('status') === 'confirmed' ? 'selected' : ''); ?>>Confirmed</option>
                <option value="in_progress" <?php echo e(request('status') === 'in_progress' ? 'selected' : ''); ?>>In Progress</option>
                <option value="completed" <?php echo e(request('status') === 'completed' ? 'selected' : ''); ?>>Completed</option>
                <option value="cancelled" <?php echo e(request('status') === 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
            </select>
        </div>

        <div class="filter-group">
            <label for="priority">Priority:</label>
            <select name="priority" id="priority" onchange="this.form.submit()">
                <option value="all" <?php echo e(request('priority') === 'all' || !request('priority') ? 'selected' : ''); ?>>All Priorities</option>
                <option value="emergency" <?php echo e(request('priority') === 'emergency' ? 'selected' : ''); ?>>Emergency</option>
                <option value="high" <?php echo e(request('priority') === 'high' ? 'selected' : ''); ?>>High</option>
                <option value="medium" <?php echo e(request('priority') === 'medium' ? 'selected' : ''); ?>>Medium</option>
                <option value="low" <?php echo e(request('priority') === 'low' ? 'selected' : ''); ?>>Low</option>
            </select>
        </div>

        <div class="filter-group search-group">
            <label for="search">Search:</label>
            <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" 
                   placeholder="Search consultations..." class="search-input">
            <button type="submit" class="search-btn">🔍</button>
        </div>

        <?php if(request()->hasAny(['status', 'priority', 'search'])): ?>
            <a href="<?php echo e(route('consultation.index')); ?>" class="clear-filters">Clear Filters</a>
        <?php endif; ?>
    </form>
</div>

<!-- Consultations List -->
<div class="consultations-container">
    <?php if($consultations->count() > 0): ?>
        <div class="consultations-grid">
            <?php $__currentLoopData = $consultations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $consultation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="consultation-card">
                    <div class="card-header">
                        <div class="pet-info">
                            <h3><?php echo e($consultation->petHealthRecord->pet_name); ?></h3>
                            <span class="pet-species"><?php echo e($consultation->petHealthRecord->species); ?></span>
                        </div>
                        <div class="status-badges">
                            <span class="badge badge-<?php echo e($consultation->status_badge); ?>">
                                <?php echo e(ucfirst($consultation->status)); ?>

                            </span>
                            <span class="badge badge-<?php echo e($consultation->priority_badge); ?> priority-badge">
                                <?php echo e(ucfirst($consultation->priority)); ?>

                            </span>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="consultation-info">
                            <p class="consultation-type">
                                <strong>Type:</strong> <?php echo e($consultation::getConsultationTypes()[$consultation->consultation_type]); ?>

                            </p>
                            <p class="symptoms">
                                <strong>Symptoms:</strong> 
                                <?php echo e(Str::limit($consultation->symptoms_description, 100)); ?>

                            </p>
                            <p class="date-info">
                                <strong>Preferred Date:</strong> <?php echo e($consultation->formatted_preferred_date); ?>

                            </p>
                            <?php if($consultation->scheduled_date): ?>
                                <p class="scheduled-date">
                                    <strong>Scheduled:</strong> <?php echo e($consultation->formatted_scheduled_date); ?>

                                </p>
                            <?php endif; ?>
                            <?php if($consultation->veterinarian_name): ?>
                                <p class="vet-info">
                                    <strong>Veterinarian:</strong> <?php echo e($consultation->veterinarian_name); ?>

                                </p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="action-buttons">
                            <a href="<?php echo e(route('consultation.show', $consultation)); ?>" class="btn btn-sm btn-outline">
                                View Details
                            </a>
                            
                            <?php if($consultation->can_be_rescheduled): ?>
                                <a href="<?php echo e(route('consultation.edit', $consultation)); ?>" class="btn btn-sm btn-secondary">
                                    Edit
                                </a>
                            <?php endif; ?>

                            <?php if($consultation->status === 'completed' && !$consultation->rating): ?>
                                <button class="btn btn-sm btn-success" onclick="showFeedbackModal(<?php echo e($consultation->id); ?>)">
                                    Rate
                                </button>
                            <?php endif; ?>
                        </div>
                        <small class="created-date">
                            Created <?php echo e($consultation->created_at->diffForHumans()); ?>

                        </small>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <div class="pagination-wrapper">
            <?php echo e($consultations->links()); ?>

        </div>
    <?php else: ?>
        <div class="empty-state">
            <div class="empty-icon">🩺</div>
            <h3>No Consultations Found</h3>
            <p>
                <?php if(request()->hasAny(['status', 'priority', 'search'])): ?>
                    No consultations match your current filters. Try adjusting your search criteria.
                <?php else: ?>
                    You haven't booked any consultations yet. Start by booking your first consultation!
                <?php endif; ?>
            </p>
            <?php if(!request()->hasAny(['status', 'priority', 'search'])): ?>
                <a href="<?php echo e(route('consultation.create')); ?>" class="btn btn-primary">
                    Book Your First Consultation
                </a>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<style>
.consultation-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px 30px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.header-content h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
}

.header-content p {
    margin: 0 0 20px 0;
    opacity: 0.9;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    font-size: 24px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-icon.total { background: #e3f2fd; }
.stat-icon.pending { background: #fff3e0; }
.stat-icon.confirmed { background: #e8f5e8; }
.stat-icon.completed { background: #f3e5f5; }

.stat-content h3 {
    margin: 0;
    font-size: 24px;
    color: #2c3e50;
}

.stat-content p {
    margin: 0;
    color: #7f8c8d;
    font-size: 14px;
}

.filters-section {
    background: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.filters-form {
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 500;
    color: #2c3e50;
    font-size: 14px;
}

.filter-group select,
.search-input {
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 14px;
}

.search-group {
    flex: 1;
    min-width: 250px;
    position: relative;
}

.search-input {
    width: 100%;
    padding-right: 40px;
}

.search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.clear-filters {
    color: #e74c3c;
    text-decoration: none;
    font-size: 14px;
    align-self: center;
}

.consultations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.consultation-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.consultation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: start;
}

.pet-info h3 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.pet-species {
    color: #7f8c8d;
    font-size: 14px;
}

.status-badges {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.badge-warning { background: #fff3cd; color: #856404; }
.badge-info { background: #d1ecf1; color: #0c5460; }
.badge-primary { background: #d1ecf1; color: #0c5460; }
.badge-success { background: #d4edda; color: #155724; }
.badge-danger { background: #f8d7da; color: #721c24; }

.card-body {
    padding: 20px;
}

.consultation-info p {
    margin: 0 0 10px 0;
    font-size: 14px;
    line-height: 1.5;
}

.card-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.btn-outline {
    background: transparent;
    color: #3498db;
    border: 1px solid #3498db;
}

.btn-outline:hover {
    background: #3498db;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-success {
    background: #28a745;
    color: white;
}

.created-date {
    color: #7f8c8d;
    font-size: 12px;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.empty-state p {
    color: #7f8c8d;
    margin-bottom: 20px;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\pawportal\resources\views/consultation/index.blade.php ENDPATH**/ ?>