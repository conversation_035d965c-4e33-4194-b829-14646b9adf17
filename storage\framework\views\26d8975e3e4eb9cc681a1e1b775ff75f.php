<?php $__env->startSection('title', 'Consultation Details'); ?>

<?php $__env->startSection('content'); ?>
<div class="consultation-header">
    <div class="header-content">
        <div class="header-left">
            <h1>🩺 Consultation Details</h1>
            <div class="consultation-meta">
                <span class="badge badge-<?php echo e($consultation->status_badge); ?>">
                    <?php echo e(ucfirst($consultation->status)); ?>

                </span>
                <span class="badge badge-<?php echo e($consultation->priority_badge); ?> priority-badge">
                    <?php echo e(ucfirst($consultation->priority)); ?> Priority
                </span>
                <span class="consultation-id">ID: #<?php echo e($consultation->id); ?></span>
            </div>
        </div>
        <div class="header-actions">
            <a href="<?php echo e(route('consultation.index')); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <?php if($consultation->can_be_rescheduled): ?>
                <a href="<?php echo e(route('consultation.edit', $consultation)); ?>" class="btn btn-primary">
                    <i class="fas fa-edit"></i> Edit
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<div class="consultation-details">
    <div class="details-grid">
        <!-- Pet Information -->
        <div class="detail-card">
            <h3>🐾 Pet Information</h3>
            <div class="detail-content">
                <div class="detail-item">
                    <label>Pet Name:</label>
                    <span><?php echo e($consultation->petHealthRecord->pet_name); ?></span>
                </div>
                <div class="detail-item">
                    <label>Species:</label>
                    <span><?php echo e($consultation->petHealthRecord->species); ?></span>
                </div>
                <?php if($consultation->petHealthRecord->breed): ?>
                <div class="detail-item">
                    <label>Breed:</label>
                    <span><?php echo e($consultation->petHealthRecord->breed); ?></span>
                </div>
                <?php endif; ?>
                <?php if($consultation->petHealthRecord->last_vaccination): ?>
                <div class="detail-item">
                    <label>Last Vaccination:</label>
                    <span><?php echo e(\Carbon\Carbon::parse($consultation->petHealthRecord->last_vaccination)->format('M d, Y')); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Consultation Information -->
        <div class="detail-card">
            <h3>📋 Consultation Information</h3>
            <div class="detail-content">
                <div class="detail-item">
                    <label>Type:</label>
                    <span><?php echo e($consultation::getConsultationTypes()[$consultation->consultation_type]); ?></span>
                </div>
                <div class="detail-item">
                    <label>Priority:</label>
                    <span class="badge badge-<?php echo e($consultation->priority_badge); ?>">
                        <?php echo e(ucfirst($consultation->priority)); ?>

                    </span>
                </div>
                <div class="detail-item">
                    <label>Preferred Date:</label>
                    <span><?php echo e($consultation->formatted_preferred_date); ?></span>
                </div>
                <?php if($consultation->scheduled_date): ?>
                <div class="detail-item">
                    <label>Scheduled Date:</label>
                    <span class="scheduled-date"><?php echo e($consultation->formatted_scheduled_date); ?></span>
                </div>
                <?php endif; ?>
                <div class="detail-item">
                    <label>Time Slot:</label>
                    <span><?php echo e($consultation::getTimeSlots()[$consultation->time_slot]); ?></span>
                </div>
                <div class="detail-item">
                    <label>Meeting Platform:</label>
                    <span><?php echo e($consultation::getMeetingPlatforms()[$consultation->meeting_platform]); ?></span>
                </div>
                <?php if($consultation->consultation_fee): ?>
                <div class="detail-item">
                    <label>Consultation Fee:</label>
                    <span class="fee">$<?php echo e(number_format($consultation->consultation_fee, 2)); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Symptoms & Description -->
        <div class="detail-card full-width">
            <h3>🔍 Symptoms & Description</h3>
            <div class="detail-content">
                <div class="symptoms-text">
                    <?php echo e($consultation->symptoms_description); ?>

                </div>
                <?php if($consultation->additional_notes): ?>
                <div class="additional-notes">
                    <h4>Additional Notes:</h4>
                    <p><?php echo e($consultation->additional_notes); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Veterinarian Information -->
        <?php if($consultation->veterinarian_name || $consultation->status !== 'pending'): ?>
        <div class="detail-card">
            <h3>👨‍⚕️ Veterinarian Information</h3>
            <div class="detail-content">
                <?php if($consultation->veterinarian_name): ?>
                <div class="detail-item">
                    <label>Name:</label>
                    <span><?php echo e($consultation->veterinarian_name); ?></span>
                </div>
                <?php endif; ?>
                <?php if($consultation->veterinarian_email): ?>
                <div class="detail-item">
                    <label>Email:</label>
                    <span><?php echo e($consultation->veterinarian_email); ?></span>
                </div>
                <?php endif; ?>
                <?php if($consultation->veterinarian_phone): ?>
                <div class="detail-item">
                    <label>Phone:</label>
                    <span><?php echo e($consultation->veterinarian_phone); ?></span>
                </div>
                <?php endif; ?>
                <?php if(!$consultation->veterinarian_name && $consultation->status === 'confirmed'): ?>
                <p class="pending-assignment">Veterinarian will be assigned soon.</p>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Meeting Details -->
        <?php if($consultation->meeting_link || $consultation->meeting_id): ?>
        <div class="detail-card">
            <h3>💻 Meeting Details</h3>
            <div class="detail-content">
                <?php if($consultation->meeting_link): ?>
                <div class="detail-item">
                    <label>Meeting Link:</label>
                    <a href="<?php echo e($consultation->meeting_link); ?>" target="_blank" class="meeting-link">
                        Join Meeting <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
                <?php endif; ?>
                <?php if($consultation->meeting_id): ?>
                <div class="detail-item">
                    <label>Meeting ID:</label>
                    <span class="meeting-id"><?php echo e($consultation->meeting_id); ?></span>
                </div>
                <?php endif; ?>
                <?php if($consultation->meeting_instructions): ?>
                <div class="detail-item">
                    <label>Instructions:</label>
                    <div class="meeting-instructions"><?php echo e($consultation->meeting_instructions); ?></div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Diagnosis & Treatment -->
        <?php if($consultation->diagnosis || $consultation->treatment_plan || $consultation->prescription): ?>
        <div class="detail-card full-width">
            <h3>📝 Diagnosis & Treatment</h3>
            <div class="detail-content">
                <?php if($consultation->diagnosis): ?>
                <div class="diagnosis-section">
                    <h4>Diagnosis:</h4>
                    <p><?php echo e($consultation->diagnosis); ?></p>
                </div>
                <?php endif; ?>
                <?php if($consultation->treatment_plan): ?>
                <div class="treatment-section">
                    <h4>Treatment Plan:</h4>
                    <p><?php echo e($consultation->treatment_plan); ?></p>
                </div>
                <?php endif; ?>
                <?php if($consultation->prescription): ?>
                <div class="prescription-section">
                    <h4>Prescription:</h4>
                    <p><?php echo e($consultation->prescription); ?></p>
                </div>
                <?php endif; ?>
                <?php if($consultation->follow_up_date): ?>
                <div class="followup-section">
                    <h4>Follow-up Date:</h4>
                    <p><?php echo e(\Carbon\Carbon::parse($consultation->follow_up_date)->format('M d, Y \a\t g:i A')); ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Attachments -->
        <?php if($consultation->attachments && count($consultation->attachments) > 0): ?>
        <div class="detail-card">
            <h3>📎 Attachments</h3>
            <div class="detail-content">
                <div class="attachments-list">
                    <?php $__currentLoopData = $consultation->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="attachment-item">
                        <div class="attachment-info">
                            <i class="fas fa-file"></i>
                            <span class="filename"><?php echo e($attachment['original_name']); ?></span>
                            <span class="filesize">(<?php echo e(number_format($attachment['size'] / 1024, 1)); ?> KB)</span>
                        </div>
                        <a href="<?php echo e(route('consultation.download-attachment', [$consultation, $index])); ?>" 
                           class="download-btn">
                            <i class="fas fa-download"></i> Download
                        </a>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Veterinarian Notes -->
        <?php if($consultation->veterinarian_notes): ?>
        <div class="detail-card full-width">
            <h3>📋 Veterinarian Notes</h3>
            <div class="detail-content">
                <div class="vet-notes">
                    <?php echo e($consultation->veterinarian_notes); ?>

                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Rating & Feedback -->
        <?php if($consultation->status === 'completed'): ?>
        <div class="detail-card">
            <h3>⭐ Rating & Feedback</h3>
            <div class="detail-content">
                <?php if($consultation->rating): ?>
                <div class="rating-display">
                    <label>Your Rating:</label>
                    <div class="stars">
                        <?php for($i = 1; $i <= 5; $i++): ?>
                            <span class="star <?php echo e($i <= $consultation->rating ? 'filled' : ''); ?>">★</span>
                        <?php endfor; ?>
                        <span class="rating-text">(<?php echo e($consultation->rating); ?>/5)</span>
                    </div>
                </div>
                <?php if($consultation->feedback): ?>
                <div class="feedback-display">
                    <label>Your Feedback:</label>
                    <p><?php echo e($consultation->feedback); ?></p>
                </div>
                <?php endif; ?>
                <?php else: ?>
                <div class="feedback-form">
                    <p>How was your consultation experience?</p>
                    <form action="<?php echo e(route('consultation.feedback', $consultation)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="rating-input">
                            <label>Rating:</label>
                            <div class="star-rating">
                                <?php for($i = 1; $i <= 5; $i++): ?>
                                    <input type="radio" name="rating" value="<?php echo e($i); ?>" id="star<?php echo e($i); ?>" required>
                                    <label for="star<?php echo e($i); ?>" class="star-label">★</label>
                                <?php endfor; ?>
                            </div>
                        </div>
                        <div class="feedback-input">
                            <label for="feedback">Feedback (Optional):</label>
                            <textarea name="feedback" id="feedback" rows="3" 
                                      placeholder="Share your experience with this consultation..."></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit Feedback</button>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Action Buttons -->
    <div class="action-section">
        <?php if($consultation->can_be_cancelled): ?>
        <button class="btn btn-danger" onclick="showCancelModal()">
            <i class="fas fa-times"></i> Cancel Consultation
        </button>
        <?php endif; ?>

        <?php if($consultation->can_be_rescheduled): ?>
        <button class="btn btn-warning" onclick="showRescheduleModal()">
            <i class="fas fa-calendar-alt"></i> Reschedule
        </button>
        <?php endif; ?>

        <?php if($consultation->status === 'completed' && !$consultation->rating): ?>
        <button class="btn btn-success" onclick="document.querySelector('.feedback-form').scrollIntoView()">
            <i class="fas fa-star"></i> Rate Consultation
        </button>
        <?php endif; ?>
    </div>
</div>

<style>
.consultation-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: start;
    gap: 20px;
}

.header-left h1 {
    margin: 0 0 15px 0;
    font-size: 28px;
}

.consultation-meta {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.consultation-id {
    background: rgba(255,255,255,0.2);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.detail-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
}

.detail-card.full-width {
    grid-column: 1 / -1;
}

.detail-card h3 {
    background: #f8f9fa;
    margin: 0;
    padding: 20px;
    color: #2c3e50;
    font-size: 18px;
    border-bottom: 1px solid #e9ecef;
}

.detail-content {
    padding: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.detail-item label {
    font-weight: 500;
    color: #2c3e50;
    min-width: 120px;
}

.detail-item span {
    color: #7f8c8d;
    text-align: right;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.badge-warning { background: #fff3cd; color: #856404; }
.badge-info { background: #d1ecf1; color: #0c5460; }
.badge-primary { background: #d1ecf1; color: #0c5460; }
.badge-success { background: #d4edda; color: #155724; }
.badge-danger { background: #f8d7da; color: #721c24; }

.symptoms-text {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    line-height: 1.6;
    margin-bottom: 15px;
}

.additional-notes h4,
.diagnosis-section h4,
.treatment-section h4,
.prescription-section h4,
.followup-section h4 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 16px;
}

.meeting-link {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

.meeting-link:hover {
    text-decoration: underline;
}

.meeting-id {
    font-family: monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}

.attachments-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.attachment-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.download-btn {
    color: #3498db;
    text-decoration: none;
    font-size: 12px;
}

.rating-display {
    margin-bottom: 15px;
}

.stars {
    display: flex;
    align-items: center;
    gap: 5px;
}

.star {
    font-size: 20px;
    color: #ddd;
}

.star.filled {
    color: #f39c12;
}

.rating-text {
    margin-left: 10px;
    color: #7f8c8d;
    font-size: 14px;
}

.star-rating {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.star-rating input {
    display: none;
}

.star-label {
    font-size: 24px;
    color: #ddd;
    cursor: pointer;
    transition: color 0.3s ease;
}

.star-rating input:hover ~ .star-label,
.star-rating input:hover + .star-label,
.star-rating input:checked ~ .star-label,
.star-rating input:checked + .star-label {
    color: #f39c12;
}

.feedback-input textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    resize: vertical;
}

.action-section {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary { background: #3498db; color: white; }
.btn-secondary { background: #6c757d; color: white; }
.btn-danger { background: #e74c3c; color: white; }
.btn-warning { background: #f39c12; color: white; }
.btn-success { background: #27ae60; color: white; }

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        align-items: stretch;
    }
    
    .details-grid {
        grid-template-columns: 1fr;
    }
    
    .action-section {
        flex-direction: column;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\pawportal\resources\views/consultation/show.blade.php ENDPATH**/ ?>