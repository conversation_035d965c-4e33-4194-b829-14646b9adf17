<?php $__env->startSection('title', 'Edit Consultation'); ?>

<?php $__env->startSection('content'); ?>
<div class="consultation-form-header">
    <h1>✏️ Edit Consultation</h1>
    <p>Update your consultation details. Changes will require re-confirmation.</p>
</div>

<div class="form-container">
    <form action="<?php echo e(route('consultation.update', $consultation)); ?>" method="POST" enctype="multipart/form-data" class="consultation-form">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>

        <!-- Current Status Info -->
        <div class="status-info">
            <div class="status-badge">
                <span class="badge badge-<?php echo e($consultation->status_badge); ?>">
                    Current Status: <?php echo e(ucfirst($consultation->status)); ?>

                </span>
            </div>
            <p class="status-note">
                <?php if($consultation->status === 'confirmed'): ?>
                    <strong>Note:</strong> Editing a confirmed consultation will reset it to pending status for re-confirmation.
                <?php else: ?>
                    You can edit this consultation since it's still pending confirmation.
                <?php endif; ?>
            </p>
        </div>

        <!-- Pet Selection -->
        <div class="form-section">
            <h3>🐾 Select Your Pet</h3>
            <div class="form-group">
                <label for="pet_health_record_id">Choose Pet *</label>
                <select name="pet_health_record_id" id="pet_health_record_id" class="form-control <?php $__errorArgs = ['pet_health_record_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                    <option value="">Select a pet...</option>
                    <?php $__currentLoopData = $pets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pet): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($pet->id); ?>" 
                                <?php echo e((old('pet_health_record_id', $consultation->pet_health_record_id) == $pet->id) ? 'selected' : ''); ?>>
                            <?php echo e($pet->pet_name); ?> (<?php echo e($pet->species); ?>)
                            <?php if($pet->breed): ?> - <?php echo e($pet->breed); ?> <?php endif; ?>
                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <?php $__errorArgs = ['pet_health_record_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Consultation Details -->
        <div class="form-section">
            <h3>🩺 Consultation Details</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="consultation_type">Consultation Type *</label>
                    <select name="consultation_type" id="consultation_type" class="form-control <?php $__errorArgs = ['consultation_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                        <option value="">Select type...</option>
                        <?php $__currentLoopData = $consultationTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>" 
                                    <?php echo e((old('consultation_type', $consultation->consultation_type) == $key) ? 'selected' : ''); ?>>
                                <?php echo e($type); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['consultation_type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="form-group">
                    <label for="priority">Priority Level *</label>
                    <select name="priority" id="priority" class="form-control <?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                        <option value="">Select priority...</option>
                        <option value="low" <?php echo e((old('priority', $consultation->priority) == 'low') ? 'selected' : ''); ?>>
                            Low - Routine check-up
                        </option>
                        <option value="medium" <?php echo e((old('priority', $consultation->priority) == 'medium') ? 'selected' : ''); ?>>
                            Medium - Minor concerns
                        </option>
                        <option value="high" <?php echo e((old('priority', $consultation->priority) == 'high') ? 'selected' : ''); ?>>
                            High - Urgent attention needed
                        </option>
                        <option value="emergency" <?php echo e((old('priority', $consultation->priority) == 'emergency') ? 'selected' : ''); ?>>
                            Emergency - Immediate care required
                        </option>
                    </select>
                    <?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <div class="form-group">
                <label for="symptoms_description">Symptoms & Description *</label>
                <textarea name="symptoms_description" id="symptoms_description" rows="4" 
                          class="form-control <?php $__errorArgs = ['symptoms_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                          placeholder="Please describe your pet's symptoms, behavior changes, or reason for consultation in detail..."
                          required><?php echo e(old('symptoms_description', $consultation->symptoms_description)); ?></textarea>
                <small class="form-text">Be as detailed as possible to help our veterinarians prepare for your consultation.</small>
                <?php $__errorArgs = ['symptoms_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>

            <div class="form-group">
                <label for="additional_notes">Additional Notes</label>
                <textarea name="additional_notes" id="additional_notes" rows="3" 
                          class="form-control <?php $__errorArgs = ['additional_notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                          placeholder="Any additional information, previous treatments, medications, or special concerns..."><?php echo e(old('additional_notes', $consultation->additional_notes)); ?></textarea>
                <?php $__errorArgs = ['additional_notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Scheduling -->
        <div class="form-section">
            <h3>📅 Preferred Schedule</h3>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="preferred_date">Preferred Date *</label>
                    <input type="datetime-local" name="preferred_date" id="preferred_date" 
                           class="form-control <?php $__errorArgs = ['preferred_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                           value="<?php echo e(old('preferred_date', $consultation->preferred_date->format('Y-m-d\TH:i'))); ?>" 
                           min="<?php echo e(now()->addHours(2)->format('Y-m-d\TH:i')); ?>" required>
                    <small class="form-text">Please select a date and time at least 2 hours from now.</small>
                    <?php $__errorArgs = ['preferred_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>

                <div class="form-group">
                    <label for="time_slot">Preferred Time Slot *</label>
                    <select name="time_slot" id="time_slot" class="form-control <?php $__errorArgs = ['time_slot'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                        <option value="">Select time slot...</option>
                        <?php $__currentLoopData = $timeSlots; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $slot): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>" 
                                    <?php echo e((old('time_slot', $consultation->time_slot) == $key) ? 'selected' : ''); ?>>
                                <?php echo e($slot); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['time_slot'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <div class="form-group">
                <label for="meeting_platform">Preferred Meeting Platform *</label>
                <select name="meeting_platform" id="meeting_platform" class="form-control <?php $__errorArgs = ['meeting_platform'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                    <option value="">Select platform...</option>
                    <?php $__currentLoopData = $meetingPlatforms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $platform): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>" 
                                <?php echo e((old('meeting_platform', $consultation->meeting_platform) == $key) ? 'selected' : ''); ?>>
                            <?php echo e($platform); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
                <small class="form-text">We'll send you updated meeting details once your consultation is re-confirmed.</small>
                <?php $__errorArgs = ['meeting_platform'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Current Attachments -->
        <?php if($consultation->attachments && count($consultation->attachments) > 0): ?>
        <div class="form-section">
            <h3>📎 Current Attachments</h3>
            <div class="current-attachments">
                <?php $__currentLoopData = $consultation->attachments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $attachment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="attachment-item">
                    <div class="attachment-info">
                        <i class="fas fa-file"></i>
                        <span class="filename"><?php echo e($attachment['original_name']); ?></span>
                        <span class="filesize">(<?php echo e(number_format($attachment['size'] / 1024, 1)); ?> KB)</span>
                    </div>
                    <a href="<?php echo e(route('consultation.download-attachment', [$consultation, $index])); ?>" 
                       class="download-btn">
                        <i class="fas fa-download"></i> Download
                    </a>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Add New Attachments -->
        <div class="form-section">
            <h3>📎 Add New Attachments (Optional)</h3>
            <div class="form-group">
                <label for="attachments">Upload Additional Photos or Documents</label>
                <input type="file" name="attachments[]" id="attachments" 
                       class="form-control <?php $__errorArgs = ['attachments.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                       multiple accept=".jpg,.jpeg,.png,.pdf,.doc,.docx">
                <small class="form-text">
                    You can upload additional photos of symptoms, medical records, or test results. 
                    Supported formats: JPG, PNG, PDF, DOC, DOCX. Max 5MB per file.
                </small>
                <?php $__errorArgs = ['attachments.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
        </div>

        <!-- Important Information -->
        <div class="info-section">
            <h4>📋 Important Information</h4>
            <ul>
                <li><strong>Re-confirmation Required:</strong> After editing, your consultation will need to be re-confirmed by our team.</li>
                <li><strong>Status Change:</strong> Your consultation status will change to "Pending" after saving changes.</li>
                <li><strong>Meeting Details:</strong> Any existing meeting links will be updated after re-confirmation.</li>
                <li><strong>Emergency:</strong> For life-threatening emergencies, please contact your nearest emergency vet clinic immediately.</li>
            </ul>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="<?php echo e(route('consultation.show', $consultation)); ?>" class="btn btn-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Update Consultation
            </button>
        </div>
    </form>
</div>

<style>
.consultation-form-header {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    color: white;
    padding: 40px 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    text-align: center;
}

.consultation-form-header h1 {
    margin: 0 0 10px 0;
    font-size: 28px;
}

.consultation-form-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 16px;
}

.form-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
}

.consultation-form {
    padding: 40px;
}

.status-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    border-left: 4px solid #f39c12;
}

.status-badge {
    margin-bottom: 10px;
}

.status-note {
    margin: 0;
    color: #6c757d;
    font-size: 14px;
}

.form-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #f0f0f0;
}

.form-section:last-of-type {
    border-bottom: none;
}

.form-section h3 {
    color: #2c3e50;
    margin: 0 0 25px 0;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #f39c12;
    box-shadow: 0 0 0 3px rgba(243, 156, 18, 0.1);
}

.form-control.is-invalid {
    border-color: #e74c3c;
}

.invalid-feedback {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
}

.form-text {
    color: #7f8c8d;
    font-size: 12px;
    margin-top: 5px;
}

.current-attachments {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.attachment-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.download-btn {
    color: #3498db;
    text-decoration: none;
    font-size: 12px;
}

.info-section {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.info-section h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-section ul {
    margin: 0;
    padding-left: 20px;
}

.info-section li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #f39c12;
    color: white;
}

.btn-primary:hover {
    background: #e67e22;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.badge {
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.badge-warning { background: #fff3cd; color: #856404; }
.badge-info { background: #d1ecf1; color: #0c5460; }
.badge-primary { background: #d1ecf1; color: #0c5460; }
.badge-success { background: #d4edda; color: #155724; }
.badge-danger { background: #f8d7da; color: #721c24; }

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .consultation-form {
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to 2 hours from now
    const now = new Date();
    now.setHours(now.getHours() + 2);
    const minDateTime = now.toISOString().slice(0, 16);
    document.getElementById('preferred_date').min = minDateTime;

    // Priority-based styling
    const prioritySelect = document.getElementById('priority');
    prioritySelect.addEventListener('change', function() {
        const priority = this.value;
        this.className = this.className.replace(/priority-\w+/, '');
        if (priority) {
            this.classList.add(`priority-${priority}`);
        }
    });

    // File upload preview
    const fileInput = document.getElementById('attachments');
    fileInput.addEventListener('change', function() {
        const files = this.files;
        let fileInfo = '';
        for (let i = 0; i < files.length; i++) {
            fileInfo += `${files[i].name} (${(files[i].size / 1024 / 1024).toFixed(2)} MB)\n`;
        }
        if (fileInfo) {
            console.log('Selected files:', fileInfo);
        }
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\pawportal\resources\views/consultation/edit.blade.php ENDPATH**/ ?>