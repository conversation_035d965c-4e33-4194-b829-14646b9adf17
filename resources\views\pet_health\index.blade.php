@extends('layouts.app')

@section('title', 'Pet Health Records')

@section('content')
    <h1 class="page-title">My Pet Health Records</h1>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    <div class="add-button">
        <a href="{{ route('pet_health.create') }}">+ Add New Record</a>
    </div>

    @if($records->count())
        @foreach($records as $record)
            <div class="pet-card">
                <h3>{{ $record->pet_name }}</h3>
                <p><strong>Species:</strong> {{ $record->species }}</p>
                <p><strong>Breed:</strong> {{ $record->breed ?? 'N/A' }}</p>
                <p><strong>Medical History:</strong> {{ $record->medical_history ?? 'None' }}</p>
                <p><strong>Last Vaccination:</strong> {{ $record->last_vaccination ? \Carbon\Carbon::parse($record->last_vaccination)->format('M d, Y') : 'Not recorded' }}</p>

                <div class="card-actions">
                    <a href="{{ route('pet_health.edit', $record) }}" class="edit-btn">✏️ Edit</a>
                    <form method="POST" action="{{ route('pet_health.destroy', $record) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this record?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="delete-btn">🗑️ Delete</button>
                    </form>
                </div>
            </div>
        @endforeach
    @else
        <p>You haven't added any pet health records yet.</p>
    @endif

    <style>
        .page-title {
            font-size: 26px;
            color: #2c3e50;
            margin-bottom: 20px;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .add-button {
            margin-bottom: 20px;
        }

        .add-button a {
            background-color: #3498db;
            color: white;
            padding: 10px 16px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            transition: background-color 0.3s ease;
        }

        .add-button a:hover {
            background-color: #2980b9;
        }

        .pet-card {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .pet-card h3 {
            margin: 0 0 10px;
            color: #34495e;
        }

        .pet-card p {
            margin: 4px 0;
            color: #555;
        }

        .card-actions {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #eee;
            display: flex;
            gap: 10px;
        }

        .edit-btn {
            background-color: #f39c12;
            color: white;
            padding: 8px 12px;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        .edit-btn:hover {
            background-color: #e67e22;
        }

        .delete-btn {
            background-color: #e74c3c;
            color: white;
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .delete-btn:hover {
            background-color: #c0392b;
        }
    </style>
@endsection
